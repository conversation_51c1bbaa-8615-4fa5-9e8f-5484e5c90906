{"cells": [{"cell_type": "code", "execution_count": null, "id": "703d8ef2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connected to: IRSX-I640-S-19 @ 169.254.64.2\n", "[0] Saved: C:\\Users\\<USER>\\Desktop\\test_thermal_images\\thermal_000000.npy and C:\\Users\\<USER>\\Desktop\\test_thermal_images\\thermal_000000.png\n"]}], "source": ["import os\n", "import time\n", "import numpy as np\n", "import cv2\n", "from cx.device_factory import DeviceFactory\n", "\n", "def connect_camera():\n", "    dev_list = DeviceFactory.findDevices()\n", "    if not dev_list:\n", "        raise RuntimeError(\"No cameras found.\")\n", "    cam = DeviceFactory.openDevice(dev_list[0].DeviceUri)\n", "    print(f\"Connected to: {dev_list[0].DeviceModel} @ {dev_list[0].DeviceIP}\")\n", "    return cam\n", "\n", "def save_image_data(image_data, output_dir, index):\n", "    os.makedirs(output_dir, exist_ok=True)\n", "\n", "    # Save raw numpy data\n", "    raw_filename = os.path.join(output_dir, f\"thermal_{index:06d}.npy\")\n", "    np.save(raw_filename, image_data)\n", "\n", "    # Normalize to 8-bit for visualization\n", "    img_normalized = cv2.normalize(image_data, None, 0, 255, cv2.NORM_MINMAX)\n", "    img_8bit = img_normalized.astype(np.uint8)\n", "\n", "    # Optional: apply colormap (e.g., COLORMAP_JET)\n", "    img_colormap = cv2.applyColorMap(img_8bit, cv2.COLORMAP_JET)\n", "\n", "    # Save as PNG\n", "    png_filename = os.path.join(output_dir, f\"thermal_{index:06d}.png\")\n", "    cv2.imwrite(png_filename, img_colormap)\n", "\n", "    print(f\"[{index}] Saved: {raw_filename} and {png_filename}\")\n", "\n", "def acquire_images(cam, output_dir, interval_sec=5):\n", "    cam.allocAndQueueBuffer()\n", "    cam.startAcquisition()\n", "\n", "    idx = 0\n", "    try:\n", "        while True:\n", "            buffer = cam.waitF<PERSON><PERSON><PERSON><PERSON>(3000, True)\n", "            if buffer.isValid:\n", "                try:\n", "                    os.system(R\"C:\\Software\\parport\\parport.exe 53240 1\")\n", "                    img = buffer.getImage()\n", "                    save_image_data(img.data, output_dir, idx)\n", "                    idx += 1\n", "                    os.system(R\"C:\\Software\\parport\\parport.exe 53240 0\")\n", "                except OverflowError as e:\n", "                    print(f\"[{idx}] Error while getting image: {e}\")\n", "                finally:\n", "                    buffer.queueBuffer()\n", "            else:\n", "                print(f\"[{idx}] Invalid buffer received.\")\n", "            time.sleep(interval_sec)\n", "    except KeyboardInterrupt:\n", "        print(\"Acquisition stopped by user.\")\n", "\n", "if __name__ == \"__main__\":\n", "    OUTPUT_DIR = r\"C:\\Users\\<USER>\\Desktop\\test_thermal_images\"     # Directory to save images\n", "    INTERVAL_SEC = 60                 # Seconds between captures\n", "\n", "    camera = connect_camera()\n", "    acquire_images(camera, OUTPUT_DIR, INTERVAL_SEC)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}