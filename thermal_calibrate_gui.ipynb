{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b3a4bdc5", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import time\n", "import threading\n", "from datetime import datetime\n", "from pathlib import Path\n", "import queue\n", "import logging\n", "\n", "import numpy as np\n", "import cv2\n", "\n", "# Tkinter / PIL\n", "import tkinter as tk\n", "from tkinter import filedialog\n", "from tkinter import StringVar, BooleanVar, IntVar, DoubleVar\n", "from tkinter import Tk\n", "from tkinter import ttk\n", "from tkinter import messagebox\n", "from PIL import Image, ImageTk\n", "\n", "# Camera SDK\n", "from cx.device_factory import DeviceFactory\n", "\n", "# ---------------- Configuration ----------------\n", "CONFIG_PATH = Path.home() / \".thermal_calibration_gui.json\"\n", "DEFAULT_CSV = Path.home() / \"thermal_calibration.csv\"\n", "\n", "# Logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s [%(levelname)s] %(message)s\",\n", ")\n", "\n", "\n", "def ts_iso():\n", "    return datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "\n", "# ---------------- GUI App ----------------\n", "class CalibrationGUI:\n", "    def __init__(self, root: Tk):\n", "        self.root = root\n", "        self.root.title(\"Thermal Calibration GUI\")\n", "\n", "        # Camera\n", "        self.cam = None\n", "        self.preview_running = False\n", "        self.preview_thread = None\n", "        self.frame_queue = queue.Queue(maxsize=1)   # latest-wins\n", "\n", "        # Display / scaling\n", "        self.display_width = 720\n", "        self.display_height = 540\n", "        self.last_frame = None  # last raw radiometric frame (np.ndarray)\n", "        self.last_preview_bgr = None\n", "\n", "        # ROI management (image-coordinate rectangles: (x0,y0,x1,y1))\n", "        self.rois = {\n", "            \"blackbody\": None,\n", "            \"skin_shaved\": None,\n", "            \"skin_fur\": None,\n", "        }\n", "        self.active_roi = StringVar(value=\"blackbody\")\n", "        self.roi_colors = {\n", "            \"blackbody\": \"#00ff00\",\n", "            \"skin_shaved\": \"#00aaff\",\n", "            \"skin_fur\": \"#ffaa00\",\n", "        }\n", "\n", "        # ROI draw helpers\n", "        self._drag_start = None\n", "\n", "        # Settings\n", "        self.output_csv = StringVar(value=str(DEFAULT_CSV))\n", "        self.frames_per_temp = IntVar(value=10)\n", "        self.current_temp_c = DoubleVar(value=35.0)\n", "        self.fixed_scale = BooleanVar(value=False)\n", "        self.vmin_str = StringVar(value=\"\")\n", "        self.vmax_str = StringVar(value=\"\")\n", "\n", "        # Live means (for stabilization aid)\n", "        self.live_means = {\n", "            \"blackbody\": StringVar(value=\"—\"),\n", "            \"skin_shaved\": StringVar(value=\"—\"),\n", "            \"skin_fur\": StringVar(value=\"—\"),\n", "        }\n", "\n", "        # Collected data rows\n", "        # each row: {\"time\": str, \"temp_C\": float, \"bb\": float, \"shaved\": float, \"fur\": float, \"N\": int}\n", "        self.data_rows = []\n", "\n", "        # Build UI\n", "        self._build_ui()\n", "        self._load_config()\n", "\n", "        # Init camera\n", "        self._init_camera()\n", "\n", "        # UI loop for preview updates\n", "        self.root.after(30, self._process_queue)\n", "\n", "        # Clean shutdown\n", "        self.root.protocol(\"WM_DELETE_WINDOW\", self.on_close)\n", "\n", "    # ---------- UI ----------\n", "    def _build_ui(self):\n", "        self.root.columnconfigure(0, weight=1)\n", "        self.root.rowconfigure(0, weight=1)\n", "        top = ttk.Frame(self.root, padding=8)\n", "        top.grid(row=0, column=0, sticky=\"nsew\")\n", "        top.columnconfigure(1, weight=1)\n", "        top.rowconfigure(0, weight=1)\n", "\n", "        # Left: controls\n", "        ctrl = ttk.<PERSON><PERSON>(top)\n", "        ctrl.grid(row=0, column=0, sticky=\"nsw\", padx=(0, 8))\n", "        for i in range(10):\n", "            ctrl.rowconfigure(i, weight=0)\n", "        ctrl.columnconfigure(0, weight=1)\n", "\n", "        lf_cam = ttk.LabelFrame(ctrl, text=\"Camera\", padding=6)\n", "        lf_cam.grid(row=0, column=0, sticky=\"ew\", pady=4)\n", "        self.btn_connect = ttk.Button(lf_cam, text=\"Reconnect\", command=self._reconnect)\n", "        self.btn_connect.grid(row=0, column=0, padx=4, pady=2, sticky=\"w\")\n", "        self.btn_start_prev = ttk.Button(lf_cam, text=\"Start Preview\", command=self.start_preview)\n", "        self.btn_stop_prev = ttk.Button(lf_cam, text=\"Stop Preview\", command=self.stop_preview, state=\"disabled\")\n", "        self.btn_start_prev.grid(row=0, column=1, padx=4, pady=2)\n", "        self.btn_stop_prev.grid(row=0, column=2, padx=4, pady=2)\n", "\n", "        lf_disp = ttk.LabelFrame(ctrl, text=\"Display Scaling\", padding=6)\n", "        lf_disp.grid(row=1, column=0, sticky=\"ew\", pady=4)\n", "        ttk.Checkbutton(lf_disp, text=\"Fixed color scale\", variable=self.fixed_scale,\n", "                        command=lambda: None).grid(row=0, column=0, sticky=\"w\", padx=4)\n", "        ttk.Label(lf_disp, text=\"vmin:\").grid(row=1, column=0, sticky=\"e\", padx=4)\n", "        self.ent_vmin = ttk.Entry(lf_disp, textvariable=self.vmin_str, width=8)\n", "        self.ent_vmin.grid(row=1, column=1, sticky=\"w\")\n", "        ttk.Label(lf_disp, text=\"vmax:\").grid(row=1, column=2, sticky=\"e\", padx=4)\n", "        self.ent_vmax = ttk.Entry(lf_disp, textvariable=self.vmax_str, width=8)\n", "        self.ent_vmax.grid(row=1, column=3, sticky=\"w\")\n", "\n", "        lf_roi = ttk.LabelFrame(ctrl, text=\"ROIs\", padding=6)\n", "        lf_roi.grid(row=2, column=0, sticky=\"ew\", pady=4)\n", "        rbrow = ttk.Frame(lf_roi)\n", "        rbrow.grid(row=0, column=0, sticky=\"w\")\n", "        ttk.Radiobutton(rbrow, text=\"Blackbody\", value=\"blackbody\", variable=self.active_roi).grid(row=0, column=0, padx=4)\n", "        ttk.Radiobutton(rbrow, text=\"Skin (shaved)\", value=\"skin_shaved\", variable=self.active_roi).grid(row=0, column=1, padx=4)\n", "        ttk.Radiobutton(rbrow, text=\"Skin (fur)\", value=\"skin_fur\", variable=self.active_roi).grid(row=0, column=2, padx=4)\n", "        ttk.Button(lf_roi, text=\"Clear Active ROI\", command=self._clear_active_roi).grid(row=1, column=0, sticky=\"w\", padx=4, pady=4)\n", "\n", "        lf_means = ttk.LabelFrame(ctrl, text=\"Live Means (DN)\", padding=6)\n", "        lf_means.grid(row=3, column=0, sticky=\"ew\", pady=4)\n", "        self._mean_row(lf_means, \"Blackbody\", self.live_means[\"blackbody\"], 0)\n", "        self._mean_row(lf_means, \"Skin (shaved)\", self.live_means[\"skin_shaved\"], 1)\n", "        self._mean_row(lf_means, \"Skin (fur)\", self.live_means[\"skin_fur\"], 2)\n", "\n", "        lf_acq = ttk.LabelFrame(ctrl, text=\"Acquisition\", padding=6)\n", "        lf_acq.grid(row=4, column=0, sticky=\"ew\", pady=4)\n", "        ttk.Label(lf_acq, text=\"Temperature (°C):\").grid(row=0, column=0, sticky=\"e\", padx=4)\n", "        ttk.Entry(lf_acq, textvariable=self.current_temp_c, width=8).grid(row=0, column=1, sticky=\"w\")\n", "        ttk.Label(lf_acq, text=\"Frames per Temp (N):\").grid(row=1, column=0, sticky=\"e\", padx=4)\n", "        ttk.Entry(lf_acq, textvariable=self.frames_per_temp, width=8).grid(row=1, column=1, sticky=\"w\")\n", "        self.btn_acquire = ttk.But<PERSON>(lf_acq, text=\"Acquire at Current Temp\", command=self.acquire_current_temp)\n", "        self.btn_acquire.grid(row=2, column=0, columnspan=2, pady=(6, 2), sticky=\"ew\")\n", "\n", "        lf_csv = ttk.LabelFrame(ctrl, text=\"CSV Output\", padding=6)\n", "        lf_csv.grid(row=5, column=0, sticky=\"ew\", pady=4)\n", "        ttk.Label(lf_csv, text=\"CSV Path:\").grid(row=0, column=0, sticky=\"e\")\n", "        self.ent_csv = ttk.Entry(lf_csv, textvariable=self.output_csv, width=32)\n", "        self.ent_csv.grid(row=0, column=1, sticky=\"ew\", padx=4)\n", "        lf_csv.columnconfigure(1, weight=1)\n", "        ttk.Button(lf_csv, text=\"Browse\", command=self._browse_csv).grid(row=0, column=2, padx=4)\n", "        bframe = ttk.Frame(lf_csv)\n", "        bframe.grid(row=1, column=0, columnspan=3, sticky=\"ew\", pady=(6, 0))\n", "        ttk.Button(bframe, text=\"Export CSV\", command=self.export_csv).grid(row=0, column=0, padx=4)\n", "        ttk.Button(bframe, text=\"Clear Table\", command=self._clear_table).grid(row=0, column=1, padx=4)\n", "\n", "        # Right: image + table\n", "        right = ttk.<PERSON><PERSON>(top)\n", "        right.grid(row=0, column=1, sticky=\"nsew\")\n", "        right.columnconfigure(0, weight=1)\n", "        right.rowconfigure(0, weight=1)\n", "\n", "        # Image canvas for ROI drawing\n", "        self.canvas = ImageCanvas(right, width=self.display_width, height=self.display_height)\n", "        self.canvas.grid(row=0, column=0, sticky=\"nsew\", pady=(0, 6))\n", "        self.canvas.bind(\"<ButtonPress-1>\", self._on_canvas_press)\n", "        self.canvas.bind(\"<B1-Motion>\", self._on_canvas_drag)\n", "        self.canvas.bind(\"<ButtonRelease-1>\", self._on_canvas_release)\n", "\n", "        # Data table\n", "        table_frame = ttk.<PERSON><PERSON>(right)\n", "        table_frame.grid(row=1, column=0, sticky=\"nsew\")\n", "        right.rowconfigure(1, weight=0)\n", "        cols = (\"time\", \"temp_C\", \"bb\", \"shaved\", \"fur\", \"N\")\n", "        self.table = ttk.Treeview(table_frame, columns=cols, show=\"headings\", height=10)\n", "        for c, w in zip(cols, (140, 80, 110, 110, 110, 60)):\n", "            self.table.heading(c, text=c)\n", "            self.table.column(c, width=w, anchor=\"center\")\n", "        self.table.grid(row=0, column=0, sticky=\"nsew\")\n", "        table_frame.columnconfigure(0, weight=1)\n", "        table_frame.rowconfigure(0, weight=1)\n", "        ttk.Scrollbar(table_frame, orient=\"vertical\", command=self.table.yview).grid(row=0, column=1, sticky=\"ns\")\n", "        self.table.configure(yscrollcommand=lambda *args: None)\n", "\n", "        # Status bar\n", "        self.status_var = StringVar(value=\"Ready.\")\n", "        status = ttk.Label(self.root, textvariable=self.status_var, relief=\"sunken\", anchor=\"w\")\n", "        status.grid(row=1, column=0, sticky=\"ew\")\n", "\n", "    def _mean_row(self, parent, label, var, r):\n", "        ttk.Label(parent, text=label + \":\").grid(row=r, column=0, sticky=\"e\", padx=4)\n", "        ttk.Label(parent, textvariable=var, width=12).grid(row=r, column=1, sticky=\"w\")\n", "\n", "    # ---------- Camera ----------\n", "    def _init_camera(self):\n", "        try:\n", "            devs = DeviceFactory.findDevices()\n", "            if not devs:\n", "                raise RuntimeError(\"No cameras found.\")\n", "            self.cam = DeviceFactory.openDevice(devs[0].DeviceUri)\n", "            self._set_status(f\"Connected to: {devs[0].DeviceModel} @ {devs[0].DeviceIP}\")\n", "        except Exception as e:\n", "            self._set_status(f\"Camera Error: {e}\")\n", "            messagebox.showerror(\"Camera Error\", str(e))\n", "            self.cam = None\n", "\n", "    def _close_camera(self):\n", "        cam = self.cam\n", "        if cam is None:\n", "            return\n", "        for meth in (\"stopAcquisition\", \"abortAcquisition\"):\n", "            try:\n", "                getattr(cam, meth)()\n", "            except Exception:\n", "                pass\n", "        for meth in (\"deallocBuffers\", \"deallocateBuffers\", \"releaseBuffers\"):\n", "            try:\n", "                getattr(cam, meth)()\n", "            except Exception:\n", "                pass\n", "        try:\n", "            if hasattr(DeviceFactory, \"closeDevice\"):\n", "                DeviceFactory.closeDevice(cam)\n", "        except Exception:\n", "            pass\n", "        for meth in (\"close\", \"release\", \"disconnect\"):\n", "            try:\n", "                getattr(cam, meth)()\n", "            except Exception:\n", "                pass\n", "        self.cam = None\n", "\n", "    def _reconnect(self):\n", "        self.stop_preview()\n", "        self._close_camera()\n", "        self._init_camera()\n", "\n", "    # ---------- Preview ----------\n", "    def start_preview(self):\n", "        if self.cam is None:\n", "            self._init_camera()\n", "            if self.cam is None:\n", "                return\n", "        if self.preview_running:\n", "            return\n", "        self.preview_running = True\n", "        self.btn_start_prev.state([\"disabled\"])\n", "        self.btn_stop_prev.state([\"!disabled\"])\n", "        self.preview_thread = threading.Thread(target=self._preview_loop, daemon=True)\n", "        self.preview_thread.start()\n", "        self._set_status(\"Preview running...\")\n", "\n", "    def stop_preview(self):\n", "        if not self.preview_running:\n", "            return\n", "        self.preview_running = False\n", "        if self.preview_thread and self.preview_thread.is_alive():\n", "            try:\n", "                self.preview_thread.join(timeout=2.0)\n", "            except Exception:\n", "                pass\n", "        self.btn_start_prev.state([\"!disabled\"])\n", "        self.btn_stop_prev.state([\"disabled\"])\n", "        self._set_status(\"Preview stopped.\")\n", "\n", "    def _preview_loop(self):\n", "        # modest rate so UI stays snappy\n", "        interval = 0.25\n", "        while self.preview_running:\n", "            frame = self._capture_one_frame(timeout_ms=700)\n", "            if frame is not None:\n", "                self.last_frame = frame\n", "                preview = self._make_preview_bgr(frame)\n", "                self.last_preview_bgr = preview\n", "                item = {\"frame\": frame, \"preview\": preview}\n", "                try:\n", "                    self.frame_queue.put_nowait(item)\n", "                except queue.Full:\n", "                    try:\n", "                        _ = self.frame_queue.get_nowait()\n", "                    except queue.Empty:\n", "                        pass\n", "                    self.frame_queue.put_nowait(item)\n", "            time.sleep(interval)\n", "\n", "    def _capture_one_frame(self, timeout_ms: int):\n", "        if self.cam is None:\n", "            return None\n", "        try:\n", "            # Allocate a couple of buffers, start, wait, extract, stop, dealloc\n", "            try:\n", "                self.cam.allocAndQueueBuffer()\n", "            except Exception:\n", "                pass\n", "            try:\n", "                self.cam.allocAndQueueBuffer()\n", "            except Exception:\n", "                pass\n", "\n", "            self.cam.startAcquisition()\n", "            try:\n", "                b = self.cam.waitFor<PERSON><PERSON><PERSON>(int(timeout_ms), True)\n", "            except Exception:\n", "                b = None\n", "            if b is None or not hasattr(b, \"isValid\") or not b.isValid:\n", "                try:\n", "                    self.cam.stopAcquisition()\n", "                except Exception:\n", "                    pass\n", "                self._dealloc_all_buffers()\n", "                return None\n", "            try:\n", "                img = b.getImage()\n", "                data = img.data.copy()\n", "                if data.ndim > 2:\n", "                    data = data.squeeze()\n", "            except Exception:\n", "                data = None\n", "\n", "            try:\n", "                if hasattr(b, \"queueBuffer\"):\n", "                    b.queue<PERSON><PERSON><PERSON>()\n", "            except Exception:\n", "                pass\n", "            try:\n", "                self.cam.stopAcquisition()\n", "            except Exception:\n", "                pass\n", "            self._dealloc_all_buffers()\n", "            return data\n", "        except Exception as e:\n", "            logging.debug(f\"Single-shot failed: {e}\")\n", "            try:\n", "                self.cam.stopAcquisition()\n", "            except Exception:\n", "                pass\n", "            self._dealloc_all_buffers()\n", "            return None\n", "\n", "    def _dealloc_all_buffers(self):\n", "        for meth in (\"deallocBuffers\", \"deallocateBuffers\", \"releaseBuffers\"):\n", "            try:\n", "                if hasattr(self.cam, meth):\n", "                    getattr(self.cam, meth)()\n", "            except Exception:\n", "                pass\n", "\n", "    # ---------- ROI drawing & display ----------\n", "    def _on_canvas_press(self, event):\n", "        if self.last_preview_bgr is None:\n", "            return\n", "        self._drag_start = (event.x, event.y)\n", "\n", "    def _on_canvas_drag(self, event):\n", "        if self._drag_start is None or self.last_preview_bgr is None:\n", "            return\n", "        x0, y0 = self._drag_start\n", "        x1, y1 = event.x, event.y\n", "        rect = (min(x0, x1), min(y0, y1), max(x0, x1), max(y0, y1))\n", "        self._draw_temp_rect_on_canvas(rect)\n", "\n", "    def _on_canvas_release(self, event):\n", "        if self._drag_start is None or self.last_preview_bgr is None:\n", "            return\n", "        x0, y0 = self._drag_start\n", "        x1, y1 = event.x, event.y\n", "        self._drag_start = None\n", "        if abs(x1 - x0) < 4 or abs(y1 - y0) < 4:\n", "            return\n", "        # Convert canvas coords to image coords\n", "        rect_canvas = (min(x0, x1), min(y0, y1), max(x0, x1), max(y0, y1))\n", "        rect_img = self.canvas.canvas_rect_to_image_rect(rect_canvas)\n", "        key = self.active_roi.get()\n", "        self.rois[key] = rect_img\n", "        self._redraw_rois()\n", "\n", "    def _draw_temp_rect_on_canvas(self, rect_canvas):\n", "        # show temporary rectangle while dragging\n", "        self.canvas.set_overlay_temp(rect_canvas, self.roi_colors[self.active_roi.get()])\n", "\n", "    def _redraw_rois(self):\n", "        self.canvas.clear_overlays()\n", "        for key, rect_img in self.rois.items():\n", "            if rect_img is None:\n", "                continue\n", "            col = self.roi_colors[key]\n", "            rect_canvas = self.canvas.image_rect_to_canvas_rect(rect_img)\n", "            self.canvas.draw_named_roi(rect_canvas, label=key.replace(\"_\", \" \"), color=col)\n", "\n", "    # ---------- Processing queue (UI thread) ----------\n", "    def _process_queue(self):\n", "        last = None\n", "        try:\n", "            while True:\n", "                last = self.frame_queue.get_nowait()\n", "        except queue.Empty:\n", "            pass\n", "\n", "        if last is not None:\n", "            # update preview image\n", "            self.canvas.show_bgr(last[\"preview\"])\n", "\n", "            # update live means if ROIs exist\n", "            for key in (\"blackbody\", \"skin_shaved\", \"skin_fur\"):\n", "                val = self._roi_mean(last[\"frame\"], self.rois[key])\n", "                self.live_means[key].set(f\"{val:.1f}\" if np.isfinite(val) else \"—\")\n", "            # redraw persistent ROI boxes\n", "            self._redraw_rois()\n", "\n", "        self.root.after(30, self._process_queue)\n", "\n", "    # ---------- Preview colormap ----------\n", "    def _make_preview_bgr(self, array2d: np.ndarray) -> np.ndarray:\n", "        img = array2d.astype(np.float32)\n", "        if self.fixed_scale.get():\n", "            try:\n", "                vmin = float(self.vmin_str.get())\n", "                vmax = float(self.vmax_str.get())\n", "                if vmax <= vmin:\n", "                    raise ValueError\n", "                rng = max(vmax - vmin, 1e-12)\n", "                img8 = ((np.clip(img, vmin, vmax) - vmin) / rng * 255.0).astype(np.uint8)\n", "            except Exception:\n", "                img8 = cv2.normalize(img, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)\n", "        else:\n", "            img8 = cv2.normalize(img, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)\n", "        bgr = cv2.applyColorMap(img8, cv2.COLORMAP_JET)\n", "        # fit to canvas size and record mapping\n", "        bgr = self.canvas.fit_to_canvas(bgr)\n", "        return bgr\n", "\n", "    # ---------- ROI utils ----------\n", "    def _roi_mean(self, frame: np.n<PERSON>ray, rect_img):\n", "        try:\n", "            if rect_img is None or frame is None:\n", "                return float(\"nan\")\n", "            x0, y0, x1, y1 = map(int, rect_img)\n", "            x0 = max(0, min(x0, frame.shape[1] - 1))\n", "            x1 = max(0, min(x1, frame.shape[1]))\n", "            y0 = max(0, min(y0, frame.shape[0] - 1))\n", "            y1 = max(0, min(y1, frame.shape[0]))\n", "            if x1 <= x0 + 1 or y1 <= y0 + 1:\n", "                return float(\"nan\")\n", "            patch = frame[y0:y1, x0:x1]\n", "            return float(np.mean(patch))\n", "        except Exception:\n", "            return float(\"nan\")\n", "\n", "    def _clear_active_roi(self):\n", "        key = self.active_roi.get()\n", "        self.rois[key] = None\n", "        self._redraw_rois()\n", "\n", "    # ---------- Acquisition ----------\n", "    def acquire_current_temp(self):\n", "        if self.cam is None:\n", "            messagebox.showerror(\"<PERSON>\", \"Camera not connected.\")\n", "            return\n", "        # Ensure ROIs are set\n", "        missing = [k for k, v in self.rois.items() if v is None]\n", "        if missing:\n", "            messagebox.showwarning(\"ROIs\", f\"Please define all ROIs first: {', '.join(missing)}\")\n", "            return\n", "        try:\n", "            N = int(self.frames_per_temp.get())\n", "        except Exception:\n", "            messagebox.showerror(\"Frames\", \"Frames per Temp (N) must be an integer > 0.\")\n", "            return\n", "        if N <= 0:\n", "            messagebox.showerror(\"Frames\", \"Frames per Temp (N) must be > 0.\")\n", "            return\n", "\n", "        # Temporarily pause preview if running\n", "        was_preview = self.preview_running\n", "        if was_preview:\n", "            self.stop_preview()\n", "\n", "        tempC = float(self.current_temp_c.get())\n", "        self._set_status(f\"Acquiring {N} frames at {tempC:.2f} °C...\")\n", "        self.btn_acquire.state([\"disabled\"])\n", "        self.root.update_idletasks()\n", "\n", "        # Collect means\n", "        means = {\"blackbody\": [], \"skin_shaved\": [], \"skin_fur\": []}\n", "        acquired = 0\n", "        tries = 0\n", "        max_tries = max(50, N * 20)  # generous watchdog\n", "        while acquired < N and tries < max_tries:\n", "            tries += 1\n", "            frame = self._capture_one_frame(timeout_ms=1500)\n", "            if frame is None:\n", "                time.sleep(0.05)\n", "                continue\n", "            for key in means.keys():\n", "                means[key].append(self._roi_mean(frame, self.rois[key]))\n", "            acquired += 1\n", "            self._set_status(f\"Acquiring... {acquired}/{N}\")\n", "            self.root.update_idletasks()\n", "\n", "        if acquired < N:\n", "            messagebox.showwarning(\"Acquisition\", f\"Only acquired {acquired} / {N} frames.\")\n", "\n", "        # Average\n", "        row = {\n", "            \"time\": ts_iso(),\n", "            \"temp_C\": tempC,\n", "            \"bb\": float(np.nanmean(means[\"blackbody\"])) if means[\"blackbody\"] else float(\"nan\"),\n", "            \"shaved\": float(np.nanmean(means[\"skin_shaved\"])) if means[\"skin_shaved\"] else float(\"nan\"),\n", "            \"fur\": float(np.nanmean(means[\"skin_fur\"])) if means[\"skin_fur\"] else float(\"nan\"),\n", "            \"N\": acquired,\n", "        }\n", "        self.data_rows.append(row)\n", "        self._append_table_row(row)\n", "        self._append_csv_row(row)\n", "\n", "        self.btn_acquire.state([\"!disabled\"])\n", "        self._set_status(\"Done.\")\n", "\n", "        if was_preview:\n", "            self.start_preview()\n", "\n", "    # ---------- Table / CSV ----------\n", "    def _append_table_row(self, row):\n", "        values = (\n", "            row[\"time\"],\n", "            f'{row[\"temp_C\"]:.2f}',\n", "            f'{row[\"bb\"]:.3f}',\n", "            f'{row[\"shaved\"]:.3f}',\n", "            f'{row[\"fur\"]:.3f}',\n", "            row[\"N\"],\n", "        )\n", "        self.table.insert(\"\", \"end\", values=values)\n", "\n", "    def _append_csv_row(self, row):\n", "        path = Path(self.output_csv.get()).expanduser()\n", "        header_needed = not path.exists()\n", "        try:\n", "            path.parent.mkdir(parents=True, exist_ok=True)\n", "            with open(path, \"a\", encoding=\"utf-8\") as f:\n", "                if header_needed:\n", "                    f.write(\"Timestamp,Temperature_C,Blackbody_DN,SkinShaved_DN,SkinFur_DN,N_frames\\n\")\n", "                f.write(\n", "                    f'{row[\"time\"]},{row[\"temp_C\"]:.6f},{row[\"bb\"]:.6f},{row[\"shaved\"]:.6f},{row[\"fur\"]:.6f},{row[\"N\"]}\\n'\n", "                )\n", "        except Exception as e:\n", "            messagebox.showerror(\"CSV\", f\"Failed to write CSV: {e}\")\n", "\n", "    def export_csv(self):\n", "        \"\"\"Re-write CSV from current table buffer (useful if you cleared file).\"\"\"\n", "        path = Path(self.output_csv.get()).expanduser()\n", "        try:\n", "            path.parent.mkdir(parents=True, exist_ok=True)\n", "            with open(path, \"w\", encoding=\"utf-8\") as f:\n", "                f.write(\"Timestamp,Temperature_C,Blackbody_DN,SkinShaved_DN,SkinFur_DN,N_frames\\n\")\n", "                for r in self.data_rows:\n", "                    f.write(\n", "                        f'{r[\"time\"]},{r[\"temp_C\"]:.6f},{r[\"bb\"]:.6f},{r[\"shaved\"]:.6f},{r[\"fur\"]:.6f},{r[\"N\"]}\\n'\n", "                    )\n", "            messagebox.showinfo(\"CSV\", f\"Exported to {path}\")\n", "        except Exception as e:\n", "            messagebox.showerror(\"CSV\", f\"Failed to export CSV: {e}\")\n", "\n", "    def _browse_csv(self):\n", "        path = filedialog.asksaveasfilename(\n", "            defaultextension=\".csv\",\n", "            filetypes=[(\"CSV\", \"*.csv\")],\n", "            initialfile=Path(self.output_csv.get()).name,\n", "            title=\"Select CSV file\",\n", "        )\n", "        if path:\n", "            self.output_csv.set(path)\n", "\n", "    def _clear_table(self):\n", "        self.data_rows.clear()\n", "        for iid in self.table.get_children():\n", "            self.table.delete(iid)\n", "\n", "    # ---------- Config ----------\n", "    def _load_config(self):\n", "        try:\n", "            if CONFIG_PATH.exists():\n", "                cfg = json.loads(CONFIG_PATH.read_text(encoding=\"utf-8\"))\n", "                self.output_csv.set(cfg.get(\"output_csv\", self.output_csv.get()))\n", "                self.frames_per_temp.set(cfg.get(\"frames_per_temp\", 10))\n", "                self.current_temp_c.set(cfg.get(\"current_temp_c\", 35.0))\n", "                self.fixed_scale.set(cfg.get(\"fixed_scale\", False))\n", "                self.vmin_str.set(cfg.get(\"vmin_str\", \"\"))\n", "                self.vmax_str.set(cfg.get(\"vmax_str\", \"\"))\n", "        except Exception as e:\n", "            logging.warning(f\"Failed to load config: {e}\")\n", "\n", "    def _save_config(self):\n", "        try:\n", "            cfg = {\n", "                \"output_csv\": self.output_csv.get(),\n", "                \"frames_per_temp\": self.frames_per_temp.get(),\n", "                \"current_temp_c\": float(self.current_temp_c.get()),\n", "                \"fixed_scale\": self.fixed_scale.get(),\n", "                \"vmin_str\": self.vmin_str.get(),\n", "                \"vmax_str\": self.vmax_str.get(),\n", "            }\n", "            CONFIG_PATH.write_text(json.dumps(cfg, indent=2), encoding=\"utf-8\")\n", "        except Exception as e:\n", "            logging.warning(f\"Failed to save config: {e}\")\n", "\n", "    # ---------- Status ----------\n", "    def _set_status(self, text: str):\n", "        self.status_var.set(text)\n", "\n", "    # ---------- Shutdown ----------\n", "    def on_close(self):\n", "        self._save_config()\n", "        self.stop_preview()\n", "        self._close_camera()\n", "        self.root.destroy()\n", "\n", "\n", "# ---------- Canvas wrapper with ROI overlays and coordinate mapping ----------\n", "class ImageCanvas(ttk.Frame):\n", "    def __init__(self, parent, width=720, height=540):\n", "        super().__init__(parent)\n", "        self.width = width\n", "        self.height = height\n", "\n", "        # Use tk.<PERSON><PERSON> (ttk has no <PERSON><PERSON>)\n", "        self.canvas = tk.<PERSON>(self, width=width, height=height,\n", "                                highlightthickness=1, highlightbackground=\"#888\")\n", "        self.canvas.grid(row=0, column=0, sticky=\"nsew\")\n", "        self.columnconfigure(0, weight=1)\n", "        self.rowconfigure(0, weight=1)\n", "\n", "        # For convenience: allow parent to call self.bind(...)\n", "        # and forward to the inner tk.<PERSON><PERSON>\n", "        self.bind = self.canvas.bind  # type: ignore\n", "\n", "        self.photo = None\n", "        # Source image (preview) size before fitting into the canvas\n", "        self.src_w = None\n", "        self.src_h = None\n", "\n", "        # Drawn (resized) image size inside the canvas\n", "        self.img_w = None\n", "        self.img_h = None\n", "\n", "        # Mapping parameters: canvas -> source-image\n", "        self.scale = 1.0\n", "        self.pad_x = 0\n", "        self.pad_y = 0\n", "\n", "        # overlay items\n", "        self.temp_rect_id = None\n", "        self.named_rects = []  # list of (rect_id, text_id)\n", "\n", "    def show_bgr(self, bgr: np.n<PERSON><PERSON>):\n", "        # bgr is already padded to canvas size by fit_to_canvas()\n", "        rgb = cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)\n", "        h, w = rgb.shape[:2]\n", "        img = Image.fromarray(rgb)\n", "        self.photo = ImageTk.PhotoImage(img)\n", "        self.canvas.delete(\"img\")\n", "        self.canvas.create_image(0, 0, anchor=\"nw\", image=self.photo, tags=\"img\")\n", "\n", "    def fit_to_canvas(self, bgr: np.n<PERSON><PERSON>):\n", "        \"\"\"Resize bgr into the canvas, record scale/padding for coordinate mapping.\"\"\"\n", "        h, w = bgr.shape[:2]\n", "        self.src_w, self.src_h = w, h\n", "\n", "        sx = self.width / float(w)\n", "        sy = self.height / float(h)\n", "        self.scale = min(sx, sy)\n", "        new_w = int(round(w * self.scale))\n", "        new_h = int(round(h * self.scale))\n", "\n", "        resized = cv2.resize(bgr, (new_w, new_h), interpolation=cv2.INTER_AREA)\n", "\n", "        # Anchor top-left; to center, set pad_x/pad_y to center offsets.\n", "        self.pad_x = 0\n", "        self.pad_y = 0\n", "\n", "        pad = np.zeros((self.height, self.width, 3), dtype=resized.dtype)\n", "        pad[self.pad_y:self.pad_y + new_h, self.pad_x:self.pad_x + new_w] = resized\n", "\n", "        self.img_w = new_w\n", "        self.img_h = new_h\n", "        return pad\n", "\n", "    def canvas_rect_to_image_rect(self, rect_canvas):\n", "        \"\"\"Canvas coords -> source image coords (integers), accounting for scale & padding.\"\"\"\n", "        x0, y0, x1, y1 = rect_canvas\n", "\n", "        # remove padding\n", "        x0 -= self.pad_x\n", "        x1 -= self.pad_x\n", "        y0 -= self.pad_y\n", "        y1 -= self.pad_y\n", "\n", "        # clip to drawn image region within canvas\n", "        x0 = max(0, min(x0, self.img_w if self.img_w is not None else 0))\n", "        x1 = max(0, min(x1, self.img_w if self.img_w is not None else 0))\n", "        y0 = max(0, min(y0, self.img_h if self.img_h is not None else 0))\n", "        y1 = max(0, min(y1, self.img_h if self.img_h is not None else 0))\n", "\n", "        s = self.scale if self.scale > 0 else 1.0\n", "        # map back to source (preview) image coordinates\n", "        ix0 = int(round(x0 / s))\n", "        iy0 = int(round(y0 / s))\n", "        ix1 = int(round(x1 / s))\n", "        iy1 = int(round(y1 / s))\n", "        return (ix0, iy0, ix1, iy1)\n", "\n", "    def image_rect_to_canvas_rect(self, rect_img):\n", "        \"\"\"Source image coords -> canvas coords, for drawing overlays.\"\"\"\n", "        x0, y0, x1, y1 = rect_img\n", "        s = self.scale if self.scale > 0 else 1.0\n", "        cx0 = int(round(x0 * s)) + self.pad_x\n", "        cy0 = int(round(y0 * s)) + self.pad_y\n", "        cx1 = int(round(x1 * s)) + self.pad_x\n", "        cy1 = int(round(y1 * s)) + self.pad_y\n", "        return (cx0, cy0, cx1, cy1)\n", "\n", "    def set_overlay_temp(self, rect_canvas, color=\"#00ff00\"):\n", "        # temp rectangle during drag\n", "        if self.temp_rect_id is not None:\n", "            self.canvas.delete(self.temp_rect_id)\n", "            self.temp_rect_id = None\n", "        x0, y0, x1, y1 = rect_canvas\n", "        self.temp_rect_id = self.canvas.create_rectangle(\n", "            x0, y0, x1, y1, outline=color, width=2, dash=(3, 2)\n", "        )\n", "\n", "    def clear_overlays(self):\n", "        if self.temp_rect_id is not None:\n", "            self.canvas.delete(self.temp_rect_id)\n", "            self.temp_rect_id = None\n", "        for rid, tid in self.named_rects:\n", "            try:\n", "                self.canvas.delete(rid)\n", "                self.canvas.delete(tid)\n", "            except Exception:\n", "                pass\n", "        self.named_rects.clear()\n", "\n", "    def draw_named_roi(self, rect_canvas, label=\"ROI\", color=\"#00ff00\"):\n", "        x0, y0, x1, y1 = rect_canvas\n", "        rid = self.canvas.create_rectangle(x0, y0, x1, y1, outline=color, width=2)\n", "        tid = self.canvas.create_text(x0 + 4, y0 + 12, text=label, fill=color, anchor=\"w\")\n", "        self.named_rects.append((rid, tid))\n", "\n", "\n", "# ---------- Main ----------\n", "if __name__ == \"__main__\":\n", "    root = Tk()\n", "    # Use ttk theme if available\n", "    try:\n", "        style = ttk.Style()\n", "        if \"vista\" in style.theme_names():\n", "            style.theme_use(\"vista\")\n", "        elif \"clam\" in style.theme_names():\n", "            style.theme_use(\"clam\")\n", "    except Exception:\n", "        pass\n", "\n", "    app = CalibrationGUI(root)\n", "    root.mainloop()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}