{"cells": [{"cell_type": "code", "execution_count": 2, "id": "24056def", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import time\n", "import threading\n", "from collections import deque\n", "from datetime import datetime\n", "from pathlib import Path\n", "import queue\n", "import subprocess\n", "import logging\n", "import shutil  # for disk usage\n", "\n", "import numpy as np\n", "import cv2\n", "from tkinter import filedialog\n", "from tkinter import StringVar, BooleanVar\n", "from tkinter import Tk\n", "from tkinter import ttk\n", "from tkinter import messagebox\n", "from PIL import Image, ImageTk\n", "\n", "import matplotlib\n", "matplotlib.use(\"TkAgg\")\n", "from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg\n", "from matplotlib.figure import Figure\n", "from matplotlib import dates as mdates\n", "\n", "from cx.device_factory import DeviceFactory\n", "\n", "# ---------------- Configuration ----------------\n", "PARPORT_EXE = r\"C:\\\\Software\\\\parport\\\\parport.exe\"\n", "PARPORT_ADDR = \"53240\"  # adjust if needed\n", "CONFIG_PATH = Path.home() / \".thermal_capture_config.json\"\n", "\n", "# Logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s [%(levelname)s] %(message)s\",\n", ")\n", "\n", "\n", "# ---- Helpers for robustness ----\n", "def _epoch_to_mpl(t_unix: float) -> float:\n", "    \"\"\"Robust conversion to Matplotlib date float, works even if epoch2num is missing.\"\"\"\n", "    try:\n", "        return mdates.epoch2num(t_unix)  # not present in some older Matplotlib builds\n", "    except Exception:\n", "        return mdates.date2num(datetime.fromtimestamp(t_unix))\n", "\n", "\n", "class ThermalCaptureApp:\n", "    def __init__(self, root: Tk):\n", "        self.root = root\n", "        self.root.title(\"Thermal Capture GUI\")\n", "\n", "        # Internal state\n", "        self.running = False          # True only while capture thread is active\n", "        self.is_paused = False        # Paused state between sessions\n", "        self.cam = None\n", "        self.thread = None\n", "        self.writer_thread = None\n", "        self.writer_queue: \"queue.Queue[dict]\" = queue.Queue(maxsize=128)\n", "        self.stop_writer = threading.Event()\n", "\n", "        # UI update queue (latest-wins, capacity 1)\n", "        self.frame_queue = queue.Queue(maxsize=1)\n", "        self.status_queue = queue.Queue(maxsize=50)  # UI status messages from worker threads\n", "\n", "        # Plot updates\n", "        self.last_plot_draw = 0.0\n", "        self.redraw_interval = 0.10  # seconds between plot draws (snappier)\n", "\n", "        self.max_stride = 1          # compute throttle for max() if desired (1 = full res)\n", "\n", "        # Rolling average of frame bytes (for time-left estimate)\n", "        self.frame_bytes_ema = None\n", "        self.ema_alpha = 0.2  # smoothing factor\n", "\n", "        # SDK buffers to use per single-shot cycle\n", "        self.num_buffers = 2\n", "\n", "        # Limit error spam\n", "        self._last_capture_error_ts = 0.0\n", "        self._err_every_sec = 0.5\n", "\n", "        # Settings (defaults)\n", "        self.output_dir = StringVar(value=r\"C:\\\\Users\\\\<USER>\\\\Videos\\\\thermal_camera\")\n", "        self.interval_sec = StringVar(value=\"5\")\n", "        self.save_npy = BooleanVar(value=True)\n", "        self.save_png = BooleanVar(value=True)      # false-color preview (on disk)\n", "        self.save_png16 = BooleanVar(value=True)    # 16-bit grayscale PNG (radiometric)\n", "        self.enable_plot = BooleanVar(value=True)   # plot created only on Start\n", "        self.fixed_scale = BooleanVar(value=False)  # fixed colormap scaling for preview\n", "        self.vmin_str = StringVar(value=\"\")\n", "        self.vmax_str = StringVar(value=\"\")\n", "        self.daily_subdir = BooleanVar(value=True)  # DEFAULT ON\n", "        self.enable_parport = BooleanVar(value=True)  # DEFAULT ON\n", "\n", "        # Plot data: store (mpl_time_float, max_value)\n", "        self.plot_data = deque(maxlen=10000)\n", "\n", "        # Plot objects (lazy-created on Start)\n", "        self.figure = None\n", "        self.plot_ax = None\n", "        self.plot_canvas = None\n", "        self.line = None  # Line2D\n", "\n", "        # UI elements\n", "        self.status_var = StringVar(value=\"Initializing...\")\n", "        self.parport_indicator = None\n", "        self._parport_indicator_bg = None\n", "\n", "        # Build UI & camera\n", "        self._build_ui()\n", "        self._load_config()\n", "        self._init_camera()\n", "\n", "        # Clean shutdown\n", "        self.root.protocol(\"WM_DELETE_WINDOW\", self.on_close)\n", "\n", "        # UI polling loop (faster)\n", "        self.root.after(20, self._process_queue)\n", "\n", "    # ---------------- UI ----------------\n", "    def _build_ui(self):\n", "        # Top-level frames\n", "        frm_top = ttk.Frame(self.root, padding=8)\n", "        frm_top.grid(row=0, column=0, sticky=\"nsew\")\n", "        self.root.columnconfigure(0, weight=1)\n", "        self.root.rowconfigure(0, weight=1)\n", "\n", "        # Capture settings\n", "        lf_capture = ttk.LabelFrame(frm_top, text=\"Capture\", padding=8)\n", "        lf_capture.grid(row=0, column=0, sticky=\"ew\", padx=5, pady=5)\n", "        lf_capture.columnconfigure(1, weight=1)\n", "\n", "        ttk.Label(lf_capture, text=\"Output Directory:\").grid(row=0, column=0, sticky=\"e\", padx=4, pady=4)\n", "        self.ent_outdir = ttk.Entry(lf_capture, textvariable=self.output_dir, width=50)\n", "        self.ent_outdir.grid(row=0, column=1, sticky=\"ew\", padx=4, pady=4)\n", "        ttk.Button(lf_capture, text=\"Browse\", command=self._browse_directory).grid(row=0, column=2, padx=4, pady=4)\n", "\n", "        ttk.Label(lf_capture, text=\"Interval (sec):\").grid(row=1, column=0, sticky=\"e\", padx=4, pady=4)\n", "        self.ent_interval = ttk.Entry(lf_capture, textvariable=self.interval_sec, width=8)\n", "        self.ent_interval.grid(row=1, column=1, sticky=\"w\", padx=4, pady=4)\n", "\n", "        # Saving options\n", "        lf_saving = ttk.LabelFrame(frm_top, text=\"Saving\", padding=8)\n", "        lf_saving.grid(row=1, column=0, sticky=\"ew\", padx=5, pady=5)\n", "        self.chk_npy = ttk.Checkbutton(lf_saving, text=\"Save .npy (raw)\", variable=self.save_npy)\n", "        self.chk_png = ttk.Checkbutton(lf_saving, text=\"Save color PNG (8-bit JET preview)\", variable=self.save_png)\n", "        self.chk_png16 = ttk.Checkbutton(lf_saving, text=\"Save 16-bit PNG (grayscale)\", variable=self.save_png16)\n", "        self.chk_npy.grid(row=0, column=0, sticky=\"w\", padx=4, pady=2)\n", "        self.chk_png.grid(row=0, column=1, sticky=\"w\", padx=4, pady=2)\n", "        self.chk_png16.grid(row=0, column=2, sticky=\"w\", padx=4, pady=2)\n", "\n", "        self.chk_daily = ttk.Checkbutton(lf_saving, text=\"Use daily subfolder\", variable=self.daily_subdir)\n", "        self.chk_daily.grid(row=1, column=0, sticky=\"w\", padx=4, pady=2, columnspan=3)\n", "\n", "        # Preview/plot options\n", "        lf_preview = ttk.LabelFrame(frm_top, text=\"Preview / Plot\", padding=8)\n", "        lf_preview.grid(row=2, column=0, sticky=\"ew\", padx=5, pady=5)\n", "        self.chk_plot = ttk.Checkbutton(lf_preview, text=\"Enable Live Plot\", variable=self.enable_plot)\n", "        self.chk_plot.grid(row=0, column=0, sticky=\"w\", padx=4, pady=2)\n", "\n", "        self.chk_fixed = ttk.Checkbutton(lf_preview, text=\"Fixed colormap scale (preview)\", variable=self.fixed_scale)\n", "        self.chk_fixed.grid(row=0, column=1, sticky=\"w\", padx=4, pady=2)\n", "        ttk.Label(lf_preview, text=\"vmin:\").grid(row=1, column=0, sticky=\"e\", padx=4, pady=2)\n", "        self.ent_vmin = ttk.Entry(lf_preview, textvariable=self.vmin_str, width=10)\n", "        self.ent_vmin.grid(row=1, column=1, sticky=\"w\", padx=4, pady=2)\n", "        ttk.Label(lf_preview, text=\"vmax:\").grid(row=1, column=2, sticky=\"e\", padx=4, pady=2)\n", "        self.ent_vmax = ttk.Entry(lf_preview, textvariable=self.vmax_str, width=10)\n", "        self.ent_vmax.grid(row=1, column=3, sticky=\"w\", padx=4, pady=2)\n", "\n", "        # Triggers\n", "        lf_trig = ttk.LabelFrame(frm_top, text=\"Triggers\", padding=8)\n", "        lf_trig.grid(row=3, column=0, sticky=\"ew\", padx=5, pady=5)\n", "        self.chk_parport = ttk.Checkbutton(lf_trig, text=\"Enable Parallel Port Trigger\", variable=self.enable_parport)\n", "        self.chk_parport.grid(row=0, column=0, sticky=\"w\", padx=4, pady=2)\n", "\n", "        # Control buttons\n", "        lf_ctrl = ttk.Frame(frm_top, padding=4)\n", "        lf_ctrl.grid(row=4, column=0, sticky=\"ew\", padx=5, pady=5)\n", "        self.btn_start = ttk.Button(lf_ctrl, text=\"Start Capture\", command=self.start_capture, width=18)\n", "        self.btn_pause = ttk.But<PERSON>(lf_ctrl, text=\"Pause Capture\", command=self.pause_capture, width=18)\n", "        self.btn_start.grid(row=0, column=0, padx=4)\n", "        self.btn_pause.grid(row=0, column=1, padx=4)\n", "        self.btn_pause.state([\"disabled\"])  # initially disabled\n", "\n", "        # Preview image\n", "        lf_img = ttk.LabelFrame(frm_top, text=\"Preview\", padding=4)\n", "        lf_img.grid(row=5, column=0, sticky=\"nsew\", padx=5, pady=5)\n", "        frm_top.rowconfigure(5, weight=1)\n", "        lf_img.columnconfigure(0, weight=1)\n", "        self.image_label = ttk.Label(lf_img, anchor=\"center\")\n", "        self.image_label.grid(row=0, column=0, sticky=\"nsew\")\n", "\n", "        # Status + Parport indicator\n", "        status_frame = ttk.Frame(self.root)\n", "        status_frame.grid(row=1, column=0, sticky=\"ew\")\n", "        status_frame.columnconfigure(0, weight=1)\n", "        self.status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=\"sunken\", anchor=\"w\")\n", "        self.status_bar.grid(row=0, column=0, sticky=\"ew\")\n", "        self.parport_indicator = ttk.Label(status_frame, text=\"PARPORT\", relief=\"groove\", width=10, anchor=\"center\")\n", "        self.parport_indicator.grid(row=0, column=1, sticky=\"e\")\n", "        self._parport_indicator_bg = self.parport_indicator.cget(\"background\")\n", "\n", "    # ---------------- Config ----------------\n", "    def _load_config(self):\n", "        try:\n", "            if CONFIG_PATH.exists():\n", "                cfg = json.load(open(CONFIG_PATH, \"r\", encoding=\"utf-8\"))\n", "                self.output_dir.set(cfg.get(\"output_dir\", self.output_dir.get()))\n", "                self.interval_sec.set(cfg.get(\"interval_sec\", self.interval_sec.get()))\n", "                self.save_npy.set(cfg.get(\"save_npy\", True))\n", "                self.save_png.set(cfg.get(\"save_png\", True))\n", "                self.save_png16.set(cfg.get(\"save_png16\", True))\n", "                self.enable_plot.set(cfg.get(\"enable_plot\", True))\n", "                self.fixed_scale.set(cfg.get(\"fixed_scale\", False))\n", "                self.vmin_str.set(cfg.get(\"vmin_str\", \"\"))\n", "                self.vmax_str.set(cfg.get(\"vmax_str\", \"\"))\n", "                self.daily_subdir.set(cfg.get(\"daily_subdir\", True))\n", "                self.enable_parport.set(cfg.get(\"enable_parport\", True))\n", "        except Exception as e:\n", "            logging.warning(f\"Failed to load config: {e}\")\n", "\n", "    def _save_config(self):\n", "        try:\n", "            cfg = {\n", "                \"output_dir\": self.output_dir.get(),\n", "                \"interval_sec\": self.interval_sec.get(),\n", "                \"save_npy\": self.save_npy.get(),\n", "                \"save_png\": self.save_png.get(),\n", "                \"save_png16\": self.save_png16.get(),\n", "                \"enable_plot\": self.enable_plot.get(),\n", "                \"fixed_scale\": self.fixed_scale.get(),\n", "                \"vmin_str\": self.vmin_str.get(),\n", "                \"vmax_str\": self.vmax_str.get(),\n", "                \"daily_subdir\": self.daily_subdir.get(),\n", "                \"enable_parport\": self.enable_parport.get(),\n", "            }\n", "            json.dump(cfg, open(CONFIG_PATH, \"w\", encoding=\"utf-8\"), indent=2)\n", "        except Exception as e:\n", "            logging.warning(f\"Failed to save config: {e}\")\n", "\n", "    # ---------------- Camera ----------------\n", "    def _init_camera(self):\n", "        try:\n", "            dev_list = DeviceFactory.findDevices()\n", "            if not dev_list:\n", "                raise RuntimeError(\"No cameras found.\")\n", "            self.cam = DeviceFactory.openDevice(dev_list[0].DeviceUri)\n", "            self._set_status(f\"Connected to: {dev_list[0].DeviceModel} @ {dev_list[0].DeviceIP}\")\n", "        except Exception as e:\n", "            self._set_status(f\"Camera Error: {e}\")\n", "            messagebox.showerror(\"Camera Error\", str(e))\n", "            self.cam = None\n", "\n", "    def _close_camera(self):\n", "        cam = self.cam\n", "        if cam is None:\n", "            return\n", "        for meth in (\"stopAcquisition\", \"abortAcquisition\"):\n", "            try:\n", "                if hasattr(cam, meth):\n", "                    getattr(cam, meth)()\n", "            except Exception:\n", "                pass\n", "        for meth in (\"deallocBuffers\", \"deallocateBuffers\", \"releaseBuffers\"):\n", "            try:\n", "                if hasattr(cam, meth):\n", "                    getattr(cam, meth)()\n", "            except Exception:\n", "                pass\n", "        try:\n", "            if hasattr(DeviceFactory, \"closeDevice\"):\n", "                DeviceFactory.closeDevice(cam)\n", "        except Exception:\n", "            pass\n", "        for meth in (\"close\", \"release\", \"disconnect\"):\n", "            try:\n", "                if hasattr(cam, meth):\n", "                    getattr(cam, meth)()\n", "            except Exception:\n", "                pass\n", "        self.cam = None\n", "\n", "    # ---------------- Helpers ----------------\n", "    def _browse_directory(self):\n", "        selected_dir = filedialog.askdirectory()\n", "        if selected_dir:\n", "            self.output_dir.set(selected_dir)\n", "\n", "    def _validate_inputs(self):\n", "        try:\n", "            interval = float(self.interval_sec.get())\n", "            if interval <= 0:\n", "                raise ValueError\n", "        except ValueError:\n", "            messagebox.showerror(\"Invalid Input\", \"Interval must be a positive number.\")\n", "            return None\n", "\n", "        if not (self.save_npy.get() or self.save_png.get() or self.save_png16.get()):\n", "            messagebox.showwarning(\"No Save Option Selected\",\n", "                                   \"Select at least one save format (.npy, 16-bit PNG, or color PNG).\")\n", "            return None\n", "\n", "        out_dir = Path(self.output_dir.get()).expanduser()\n", "        if not out_dir:\n", "            messagebox.showerror(\"Missing Output Directory\", \"Please select an output directory.\")\n", "            return None\n", "\n", "        vmin = vmax = None\n", "        if self.fixed_scale.get():\n", "            try:\n", "                vmin = float(self.vmin_str.get())\n", "                vmax = float(self.vmax_str.get())\n", "                if not (vmax > vmin):\n", "                    raise ValueError\n", "            except Exception:\n", "                messagebox.showerror(\"Invalid Scale\", \"When fixed scale is enabled, provide numeric vmin < vmax.\")\n", "                return None\n", "\n", "        return {\"interval\": interval, \"out_dir\": out_dir, \"vmin\": vmin, \"vmax\": vmax}\n", "\n", "    def _set_status(self, text: str):\n", "        self.status_var.set(text)\n", "\n", "    def _queue_status(self, text: str):\n", "        try:\n", "            self.status_queue.put_nowait(text)\n", "        except queue.Full:\n", "            try:\n", "                _ = self.status_queue.get_nowait()\n", "                self.status_queue.put_nowait(text)\n", "            except Exception:\n", "                pass\n", "\n", "    def _disable_inputs(self):\n", "        for w in (self.ent_outdir, self.ent_interval,\n", "                  self.chk_npy, self.chk_png, self.chk_png16,\n", "                  self.chk_daily, self.chk_plot, self.chk_fixed,\n", "                  self.ent_vmin, self.ent_vmax, self.chk_parport):\n", "            try:\n", "                w.state([\"disabled\"])\n", "            except Exception:\n", "                pass\n", "        self.btn_pause.state([\"!disabled\"])\n", "        self.btn_start.state([\"disabled\"])\n", "\n", "    def _enable_inputs(self):\n", "        for w in (self.ent_outdir, self.ent_interval,\n", "                  self.chk_npy, self.chk_png, self.chk_png16,\n", "                  self.chk_daily, self.chk_plot, self.chk_fixed,\n", "                  self.ent_vmin, self.ent_vmax, self.chk_parport):\n", "            try:\n", "                w.state([\"!disabled\"])\n", "            except Exception:\n", "                pass\n", "        self.btn_pause.state([\"disabled\"])\n", "        self.btn_start.state([\"!disabled\"])\n", "\n", "    def _set_paused_ui(self):\n", "        \"\"\"UI state when paused: inputs enabled, Start says 'Resume Capture'.\"\"\"\n", "        self._enable_inputs()\n", "        try:\n", "            self.btn_start.configure(text=\"Resume Capture\")\n", "        except Exception:\n", "            pass\n", "        self.is_paused = True\n", "\n", "    def _set_running_ui(self):\n", "        \"\"\"UI state when capturing: inputs disabled, Pause enabled, Start disabled & text restored.\"\"\"\n", "        try:\n", "            self.btn_start.configure(text=\"Start Capture\")\n", "        except Exception:\n", "            pass\n", "        self._disable_inputs()\n", "        self.is_paused = False\n", "\n", "    def _pulse_parport_indicator(self, duration_ms: int = 200):\n", "        try:\n", "            self.parport_indicator.configure(background=\"red\")\n", "            self.root.after(duration_ms, lambda: self.parport_indicator.configure(\n", "                background=self._parport_indicator_bg))\n", "        except Exception:\n", "            pass\n", "\n", "    # ---------------- Plot ----------------\n", "    def _create_plot_canvas(self):\n", "        if self.plot_canvas is not None:\n", "            return\n", "        self.figure = Figure(figsize=(6, 2.5), dpi=100)\n", "        self.plot_ax = self.figure.add_subplot(111)\n", "        self.plot_ax.set_title(\"Max Pixel Value Over Time\")\n", "        self.plot_ax.set_xlabel(\"Time (HH:MM)\")\n", "        self.plot_ax.set_ylabel(\"Max Value\")\n", "        self.plot_ax.grid(True)\n", "        # Configure locator/formatter once\n", "        self.plot_ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=10))\n", "        self.plot_ax.xaxis.set_major_formatter(mdates.DateFormatter(\"%H:%M\"))\n", "        (self.line,) = self.plot_ax.plot([], [], linewidth=0.8)\n", "\n", "        self.plot_canvas = FigureCanvasTkAgg(self.figure, master=self.root)\n", "        self.plot_canvas.get_tk_widget().grid(row=2, column=0, sticky=\"ew\")\n", "\n", "    def _destroy_plot_canvas(self):\n", "        if self.plot_canvas is not None:\n", "            widget = self.plot_canvas.get_tk_widget()\n", "            widget.grid_forget()\n", "            widget.destroy()\n", "            self.plot_canvas = None\n", "            self.figure = None\n", "            self.plot_ax = None\n", "            self.line = None\n", "\n", "    def _update_plot(self):\n", "        if not self.plot_data or self.plot_canvas is None or self.plot_ax is None or self.line is None:\n", "            return\n", "\n", "        # Downsample if huge\n", "        data = list(self.plot_data)\n", "        total = len(data)\n", "        if total > 5000:\n", "            step = total // 1000\n", "        elif total > 1000:\n", "            step = 5\n", "        else:\n", "            step = 1\n", "        data = data[::step]\n", "\n", "        times_mpl = [t for (t, _) in data]\n", "        max_vals = [v for (_, v) in data]\n", "\n", "        self.line.set_data(times_mpl, max_vals)\n", "        self.plot_ax.relim()\n", "        self.plot_ax.autoscale_view()\n", "        self.figure.autofmt_xdate(rotation=45)\n", "        self.plot_canvas.draw_idle()\n", "\n", "    # ---------------- File writing ----------------\n", "    @staticmethod\n", "    def _to_uint16(arr, scale=None, offset=0.0):\n", "        if arr.dtype == np.uint16:\n", "            return arr, 1.0, 0.0\n", "        if np.issubdtype(arr.dtype, np.floating):\n", "            used_scale = 100.0 if scale is None else float(scale)\n", "            enc = (arr - offset) * used_scale\n", "            enc = np.clip(enc, 0, 65535).astype(np.uint16)\n", "            return enc, used_scale, float(offset)\n", "        enc = np.clip(arr, 0, 65535).astype(np.uint16)\n", "        return enc, 1.0, 0.0\n", "\n", "    @staticmethod\n", "    def _fmt_time_left(seconds: float) -> str:\n", "        if not np.isfinite(seconds) or seconds <= 0:\n", "            return \"—\"\n", "        m, s = divmod(int(seconds), 60)\n", "        h, m = divmod(m, 60)\n", "        if h >= 48:\n", "            d, h = divmod(h, 24)\n", "            return f\"{d}d {h}h\"\n", "        if h >= 1:\n", "            return f\"{h}h {m}m\"\n", "        return f\"{m}m {s}s\"\n", "\n", "    def _writer_loop(self):\n", "        while not self.stop_writer.is_set() or not self.writer_queue.empty():\n", "            try:\n", "                job = self.writer_queue.get(timeout=0.2)\n", "            except queue.Empty:\n", "                continue\n", "            try:\n", "                if job[\"type\"] == \"save_all\":\n", "                    arr2d = job[\"array\"]\n", "                    base_path: Path = job[\"base_path\"]\n", "                    save_npy = job[\"save_npy\"]\n", "                    save_png16 = job[\"save_png16\"]\n", "                    save_preview = job[\"save_preview\"]\n", "                    preview_bgr = job.get(\"preview_bgr\")\n", "                    interval_s = float(job.get(\"interval\", 0))  # may be 0 if not provided\n", "\n", "                    base_path.parent.mkdir(parents=True, exist_ok=True)\n", "\n", "                    if save_npy:\n", "                        np.save(str(base_path) + \".npy\", arr2d)\n", "\n", "                    if save_png16:\n", "                        u16, used_scale, used_offset = self._to_uint16(arr2d)\n", "                        # Faster PNG compression\n", "                        cv2.imwrite(str(base_path) + \".png\", u16, [cv2.IMWRITE_PNG_COMPRESSION, 1])\n", "                        meta = {\n", "                            \"original_dtype\": str(arr2d.dtype),\n", "                            \"saved_as\": \"uint16-png-gray\",\n", "                            \"scale\": used_scale,\n", "                            \"offset\": used_offset,\n", "                            \"units\": \"C\",\n", "                            \"note\": \"value = (pixel / scale) + offset\",\n", "                        }\n", "                        with open(str(base_path) + \".json\", \"w\", encoding=\"utf-8\") as f:\n", "                            json.dump(meta, f, indent=2)\n", "\n", "                    if save_preview and preview_bgr is not None:\n", "                        # Faster PNG compression\n", "                        cv2.imwrite(str(base_path) + \"_preview.png\", preview_bgr, [cv2.IMWRITE_PNG_COMPRESSION, 1])\n", "\n", "                    # ---- Measure total on-disk size for this frame & occasional ETA ----\n", "                    sizes = []\n", "                    base = str(base_path)\n", "                    try:\n", "                        if save_npy:\n", "                            sizes.append(os.path.getsize(base + \".npy\"))\n", "                        if save_png16:\n", "                            sizes.append(os.path.getsize(base + \".png\"))\n", "                            sizes.append(os.path.getsize(base + \".json\"))\n", "                        if save_preview and preview_bgr is not None:\n", "                            sizes.append(os.path.getsize(base + \"_preview.png\"))\n", "                        total_bytes = float(sum(sizes))\n", "\n", "                        # Update EMA\n", "                        if self.frame_bytes_ema is None:\n", "                            self.frame_bytes_ema = total_bytes\n", "                        else:\n", "                            self.frame_bytes_ema = (self.ema_alpha * total_bytes\n", "                                                    + (1 - self.ema_alpha) * self.frame_bytes_ema)\n", "                        mb = total_bytes / (1024 * 1024)\n", "\n", "                        avg_bytes = max(self.frame_bytes_ema, 1.0)\n", "                        free_bytes = shutil.disk_usage(str(base_path.parent)).free\n", "                        frames_left = free_bytes / avg_bytes\n", "                        seconds_left = frames_left * interval_s if interval_s > 0 else float(\"inf\")\n", "                        free_gb = free_bytes / (1024 ** 3)\n", "                        eta_txt = f\" | Free: {free_gb:.1f} GB | Est. time left: {self._fmt_time_left(seconds_left)}\"\n", "\n", "                        self._queue_status(f\"Last frame size: {mb:.2f} MB{eta_txt}\")\n", "                    except Exception as e_sz:\n", "                        logging.debug(f\"Size/time-left measurement failed: {e_sz}\")\n", "\n", "            except Exception as e:\n", "                logging.error(f\"Writer error: {e}\")\n", "                self._queue_status(f\"Save error: {e}\")\n", "            finally:\n", "                self.writer_queue.task_done()\n", "\n", "    # ---------------- Capture control ----------------\n", "    def start_capture(self):\n", "        if self.cam is None:\n", "            self._init_camera()\n", "            if self.cam is None:\n", "                messagebox.showerror(\"Camera Error\", \"Camera not connected.\")\n", "                return\n", "\n", "        cfg = self._validate_inputs()\n", "        if cfg is None:\n", "            return\n", "        if self.running:\n", "            return\n", "\n", "        # If we are resuming from pause, DO NOT clear plot_data. First fresh start can clear.\n", "        if not self.is_paused:\n", "            self.plot_data.clear()\n", "\n", "        self.running = True\n", "        self._set_status(\"Capturing...\")\n", "        self._set_running_ui()\n", "\n", "        # Ensure plot exists if enabled; keep it visible across pauses\n", "        if self.enable_plot.get() and self.plot_canvas is None:\n", "            self._create_plot_canvas()\n", "\n", "        # Start writer thread (only once; keep alive across pauses)\n", "        if not (self.writer_thread and self.writer_thread.is_alive()):\n", "            self.stop_writer.clear()\n", "            self.writer_thread = threading.Thread(target=self._writer_loop, daemon=True)\n", "            self.writer_thread.start()\n", "\n", "        # Start capture thread\n", "        self.thread = threading.Thread(\n", "            target=self._capture_loop_single_shot,\n", "            args=(cfg[\"interval\"], cfg[\"out_dir\"], cfg[\"vmin\"], cfg[\"vmax\"]),\n", "            daemon=True\n", "        )\n", "        self.thread.start()\n", "\n", "    def pause_capture(self):\n", "        \"\"\"Pause (not stop): keep camera connected, keep plot & preview as-is, allow resume.\"\"\"\n", "        if not self.running:\n", "            return\n", "        self.running = False\n", "        self._set_status(\"Pausing...\")\n", "        self.root.after(50, self._after_pause_join)\n", "\n", "    def _after_pause_join(self):\n", "        if self.thread and self.thread.is_alive():\n", "            self.root.after(50, self._after_pause_join)\n", "            return\n", "        self._set_status(\"Paused\")\n", "        self._set_paused_ui()\n", "\n", "    # ---------------- Single-shot capture per tick ----------------\n", "    def _toggle_parport(self, value: int):\n", "        # Non-blocking pulse to avoid stalling capture thread\n", "        try:\n", "            subprocess.Popen(\n", "                [PARPORT_EXE, PARPORT_ADDR, str(int(bool(value)))],\n", "                stdout=subprocess.DEVNULL,\n", "                stderr=subprocess.DEVNULL\n", "            )\n", "        except Exception as e:\n", "            logging.debug(f\"Parport toggle failed: {e}\")\n", "\n", "    def _alloc_and_queue_n(self, n: int):\n", "        for _ in range(max(1, n)):\n", "            try:\n", "                self.cam.allocAndQueueBuffer()\n", "            except Exception:\n", "                pass\n", "\n", "    def _dealloc_all_buffers(self):\n", "        for meth in (\"deallocBuffers\", \"deallocateBuffers\", \"releaseBuffers\"):\n", "            try:\n", "                if hasattr(self.cam, meth):\n", "                    getattr(self.cam, meth)()\n", "            except Exception:\n", "                pass\n", "\n", "    def _make_preview_bgr(self, array2d: np.ndarray, vmin, vmax) -> np.ndarray:\n", "        # Downscale for fast UI (keep full-res for saving)\n", "        h, w = array2d.shape[:2]\n", "        target_w = 640\n", "        if w > target_w:\n", "            scale = target_w / float(w)\n", "            array2d = cv2.resize(array2d, (target_w, int(h * scale)), interpolation=cv2.INTER_AREA)\n", "\n", "        if self.fixed_scale.get() and vmin is not None and vmax is not None:\n", "            rng = max(vmax - vmin, 1e-12)\n", "            img_8bit = ((np.clip(array2d, vmin, vmax) - vmin) / rng * 255.0).astype(np.uint8)\n", "        else:\n", "            img_8bit = cv2.normalize(array2d, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)\n", "        return cv2.applyColorMap(img_8bit, cv2.COLORMAP_JET)  # BGR\n", "\n", "    def _draw_preview_to_label(self, bgr: np.n<PERSON>ray):\n", "        # No stamping at all: just display the image\n", "        rgb = cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)\n", "        img_tk = ImageTk.PhotoImage(Image.fromarray(rgb))\n", "        self.image_label.configure(image=img_tk)\n", "        self.image_label.image = img_tk\n", "\n", "    def _capture_one_frame(self, timeout_ms: int):\n", "        \"\"\"Perform a single-shot cycle: allocate+queue, start, wait, get image, stop, dealloc. Return np.ndarray or None.\"\"\"\n", "        try:\n", "            self._alloc_and_queue_n(self.num_buffers)\n", "            self.cam.startAcquisition()\n", "            try:\n", "                b = self.cam.waitFor<PERSON><PERSON><PERSON>(int(timeout_ms), True)\n", "            except Exception:\n", "                b = None\n", "            if b is None or not hasattr(b, \"isValid\") or not b.isValid:\n", "                try:\n", "                    self.cam.stopAcquisition()\n", "                except Exception:\n", "                    pass\n", "                self._dealloc_all_buffers()\n", "                return None\n", "            try:\n", "                img = b.getImage()\n", "                data = img.data.copy()\n", "            except Exception:\n", "                data = None\n", "            # Return buffer to queue (not strictly necessary before stop, but harmless)\n", "            try:\n", "                if hasattr(b, \"queueBuffer\"):\n", "                    b.queue<PERSON><PERSON><PERSON>()\n", "            except Exception:\n", "                pass\n", "            try:\n", "                self.cam.stopAcquisition()\n", "            except Exception:\n", "                pass\n", "            self._dealloc_all_buffers()\n", "            return data\n", "        except Exception as e:\n", "            logging.debug(f\"Single-shot failed: {e}\")\n", "            try:\n", "                self.cam.stopAcquisition()\n", "            except Exception:\n", "                pass\n", "            self._dealloc_all_buffers()\n", "            return None\n", "\n", "    def _capture_loop_single_shot(self, interval: float, out_dir: Path, vmin, vmax):\n", "        next_tick = time.perf_counter()  # schedule first tick immediately\n", "\n", "        while self.running:\n", "            # Sleep until scheduled tick\n", "            now = time.perf_counter()\n", "            sleep_for = next_tick - now\n", "            if sleep_for > 0:\n", "                time.sleep(sleep_for)\n", "\n", "            # Parport ON just before acquisition\n", "            if self.enable_parport.get():\n", "                self._toggle_parport(1)\n", "                self._pulse_parport_indicator()\n", "\n", "            # timeout slightly less than interval to keep schedule\n", "            timeout_ms = int(max(100, min(2000, (interval * 0.8) * 1000)))\n", "            data = self._capture_one_frame(timeout_ms)\n", "\n", "            # Parport OFF right after acquisition attempt\n", "            if self.enable_parport.get():\n", "                self._toggle_parport(0)\n", "\n", "            if data is None:\n", "                # No frame this tick; schedule next and continue\n", "                next_tick += interval\n", "                continue\n", "\n", "            try:\n", "                # Compute max with optional stride\n", "                view = data[::self.max_stride, ::self.max_stride]\n", "                max_value = float(np.max(view))\n", "\n", "                t_unix = time.time()\n", "                preview_bgr = self._make_preview_bgr(data, vmin, vmax)\n", "\n", "                # Enqueue for UI (display preview + plot point)\n", "                item = {\n", "                    \"max_val\": max_value,\n", "                    \"mpl_t\": _epoch_to_mpl(t_unix),\n", "                    \"preview_bgr\": preview_bgr,\n", "                }\n", "                try:\n", "                    self.frame_queue.put_nowait(item)\n", "                except queue.Full:\n", "                    try:\n", "                        _ = self.frame_queue.get_nowait()\n", "                    except queue.Empty:\n", "                        pass\n", "                    self.frame_queue.put_nowait(item)\n", "\n", "                # Build output path (with daily subfolder)\n", "                ts = datetime.fromtimestamp(t_unix)\n", "                base_dir = out_dir\n", "                if self.daily_subdir.get():\n", "                    base_dir = base_dir / ts.strftime(\"%Y-%m-%d\")\n", "                base_path = base_dir / f\"thermal_{ts.strftime('%Y-%m-%d_%H-%M-%S-%f')}\"\n", "\n", "                # Enqueue async save job (+ interval for time-left estimate)\n", "                try:\n", "                    self.writer_queue.put_nowait({\n", "                        \"type\": \"save_all\",\n", "                        \"array\": data,\n", "                        \"base_path\": base_path,\n", "                        \"save_npy\": self.save_npy.get(),\n", "                        \"save_png16\": self.save_png16.get(),\n", "                        \"save_preview\": self.save_png.get(),\n", "                        \"preview_bgr\": preview_bgr if self.save_png.get() else None,\n", "                        \"interval\": interval,\n", "                    })\n", "                except queue.Full:\n", "                    msg = \"Writer queue full; dropping save job.\"\n", "                    logging.warning(msg)\n", "                    self._queue_status(msg)\n", "\n", "            except Exception as e:\n", "                now_ts = time.time()\n", "                if now_ts - self._last_capture_error_ts > self._err_every_sec:\n", "                    logging.error(f\"Error during capture: {e}\")\n", "                    self._queue_status(f\"Capture error: {e}\")\n", "                    self._last_capture_error_ts = now_ts\n", "\n", "            # Schedule next tick strictly by interval (avoid drift)\n", "            next_tick += interval\n", "            if next_tick < time.perf_counter() - interval:\n", "                next_tick = time.perf_counter() + interval\n", "\n", "    # ---------------- UI loop ----------------\n", "    def _process_queue(self):\n", "        processed_any = False\n", "\n", "        # Get the newest preview item only (latest-wins)\n", "        last = None\n", "        try:\n", "            while True:\n", "                last = self.frame_queue.get_nowait()\n", "                processed_any = True\n", "        except queue.Empty:\n", "            pass\n", "\n", "        if last is not None:\n", "            # Draw preview (no timestamp overlay)\n", "            self._draw_preview_to_label(last[\"preview_bgr\"])\n", "\n", "            # Update plot data with precomputed mpl time\n", "            self.plot_data.append((last[\"mpl_t\"], last[\"max_val\"]))\n", "\n", "        # Drain status messages (show the last one received)\n", "        last_status = None\n", "        try:\n", "            while True:\n", "                last_status = self.status_queue.get_nowait()\n", "        except queue.Empty:\n", "            pass\n", "        if last_status:\n", "            self._set_status(last_status)\n", "\n", "        now = time.time()\n", "        if (\n", "            self.enable_plot.get()\n", "            and self.plot_canvas is not None\n", "            and processed_any\n", "            and (now - self.last_plot_draw) >= self.redraw_interval\n", "        ):\n", "            self._update_plot()\n", "            self.last_plot_draw = now\n", "\n", "        self.root.after(20, self._process_queue)\n", "\n", "    # ---------------- Shutdown ----------------\n", "    def on_close(self):\n", "        # Persist settings\n", "        self._save_config()\n", "\n", "        # If currently capturing, pause first\n", "        self.running = False\n", "        if self.thread and self.thread.is_alive():\n", "            try:\n", "                self.thread.join(timeout=2.0)\n", "            except Exception:\n", "                pass\n", "\n", "        # Stop writer thread\n", "        self.stop_writer.set()\n", "        if self.writer_thread and self.writer_thread.is_alive():\n", "            try:\n", "                self.writer_thread.join(timeout=2.0)\n", "            except Exception:\n", "                pass\n", "\n", "        # Close camera ONLY on app exit\n", "        self._close_camera()\n", "\n", "        # Destroy plot\n", "        self._destroy_plot_canvas()\n", "\n", "        # Close window\n", "        self.root.destroy()\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    root = Tk()\n", "    # Use ttk theme\n", "    try:\n", "        style = ttk.Style()\n", "        if \"vista\" in style.theme_names():\n", "            style.theme_use(\"vista\")\n", "        elif \"clam\" in style.theme_names():\n", "            style.theme_use(\"clam\")\n", "    except Exception:\n", "        pass\n", "\n", "    app = ThermalCaptureApp(root)\n", "    root.mainloop()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}